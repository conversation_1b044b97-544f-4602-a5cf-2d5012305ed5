use crate::{
    Command,
    frame::{Frame, StreamId},
};
use bytes::{BytesMut};
use futures::task::AtomicWaker;
use std::collections::{HashMap, VecDeque};

// Maximum buffer size per stream to prevent memory exhaustion
const MAX_STREAM_READ_BUFFER_SIZE: usize = 1024 * 1024; // 128KB per stream for reads
const MAX_OUTBOUND_FRAME_QUEUE_SIZE: usize = 100; // Maximum pending frames (reduced)
// Flow control thresholds
const FLOW_CONTROL_PAUSE_THRESHOLD: usize = MAX_STREAM_READ_BUFFER_SIZE * 3 / 4; // 75% of max
const FLOW_CONTROL_RESUME_THRESHOLD: usize = MAX_STREAM_READ_BUFFER_SIZE / 2;    // 50% of max

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum StreamStatus {
    Opening,
    Open,
    ReadClosed,   // Read end closed, write end still open
    WriteClosed,  // Write end closed, read end still open
    Closing,      // Both ends closing
    Closed,       // Both ends closed
}

#[derive(Debug)]
pub struct StreamState {
    pub status: StreamStatus,
    pub read_buffer: BytesMut,
    pub write_buffer: BytesMut,
    pub read_waker: AtomicWaker,
    pub write_waker: AtomicWaker,
    pub is_readable: bool,
    pub is_writable: bool,
    // Flow control state
    pub read_paused: bool,
    pub should_send_window_update: bool,
    // Half-close tracking
    pub read_closed: bool,   // True if read end is closed
    pub write_closed: bool,  // True if write end is closed
    pub fin_sent: bool,      // True if we sent a FIN frame
    pub fin_received: bool,  // True if we received a FIN frame
}

impl StreamState {
    pub fn new() -> Self {
        Self {
            status: StreamStatus::Opening,
            read_buffer: BytesMut::new(),
            write_buffer: BytesMut::new(),
            read_waker: AtomicWaker::new(),
            write_waker: AtomicWaker::new(),
            is_readable: false,
            is_writable: false,
            read_paused: false,
            should_send_window_update: false,
            read_closed: false,
            write_closed: false,
            fin_sent: false,
            fin_received: false,
        }
    }

    pub fn wake_readers(&mut self) {
        self.read_waker.wake();
    }

    pub fn wake_writers(&mut self) {
        self.write_waker.wake();
    }

    /// Check if we should pause reading due to full buffer
    pub fn should_pause_reading(&self) -> bool {
        self.read_buffer.len() >= FLOW_CONTROL_PAUSE_THRESHOLD
    }

    /// Check if we should resume reading
    pub fn should_resume_reading(&self) -> bool {
        self.read_paused && self.read_buffer.len() <= FLOW_CONTROL_RESUME_THRESHOLD
    }

    /// Update flow control state after buffer changes
    pub fn update_flow_control(&mut self) {
        let buffer_size = self.read_buffer.len();

        if !self.read_paused && buffer_size >= FLOW_CONTROL_PAUSE_THRESHOLD {
            self.read_paused = true;
            tracing::debug!("Stream pausing read due to buffer size: {} bytes", buffer_size);
        } else if self.read_paused && buffer_size <= FLOW_CONTROL_RESUME_THRESHOLD {
            self.read_paused = false;
            self.should_send_window_update = true;
            tracing::debug!("Stream resuming read, buffer size: {} bytes", buffer_size);
        }
    }

    /// Close the read end of the stream
    pub fn close_read(&mut self) {
        self.read_closed = true;
        self.update_status();
        self.wake_readers();
    }

    /// Close the write end of the stream
    pub fn close_write(&mut self) {
        self.write_closed = true;
        self.update_status();
        self.wake_writers();
    }

    /// Update stream status based on half-close state
    fn update_status(&mut self) {
        match (self.read_closed, self.write_closed) {
            (false, false) => {
                if self.status == StreamStatus::Opening {
                    // Keep Opening status until SynAck
                } else {
                    self.status = StreamStatus::Open;
                }
            }
            (true, false) => self.status = StreamStatus::ReadClosed,
            (false, true) => self.status = StreamStatus::WriteClosed,
            (true, true) => self.status = StreamStatus::Closed,
        }
    }

    /// Check if the stream can accept reads
    pub fn can_read(&self) -> bool {
        !self.read_closed
    }

    /// Check if the stream can accept writes
    pub fn can_write(&self) -> bool {
        !self.write_closed && matches!(self.status, StreamStatus::Open | StreamStatus::ReadClosed)
    }

    /// Check if the stream should return EOF on read
    pub fn should_return_eof(&self) -> bool {
        self.read_closed && self.read_buffer.is_empty()
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ConnectionStatus {
    Connecting,
    Connected,
    Disconnecting,
    Disconnected,
}

#[derive(Debug)]
pub struct SharedState {
    pub connection_status: ConnectionStatus,
    pub streams: HashMap<StreamId, StreamState>,
    pub outbound_frames: VecDeque<Frame>,
    pub next_stream_id: u32,
    pub connection_waker: AtomicWaker,
    pub read_backpressure: bool, // Flag to indicate if we should pause reading
    pub backpressure_waker: AtomicWaker, // Waker for when backpressure is released
}

impl SharedState {
    pub fn new() -> Self {
        Self {
            connection_status: ConnectionStatus::Connecting,
            streams: HashMap::new(),
            outbound_frames: VecDeque::new(),
            next_stream_id: 1,
            connection_waker: AtomicWaker::new(),
            read_backpressure: false,
            backpressure_waker: AtomicWaker::new(),
        }
    }

    pub fn create_stream(&mut self) -> StreamId {
        let stream_id = StreamId::new(self.next_stream_id);
        self.next_stream_id += 1;
        self.streams.insert(stream_id, StreamState::new());
        stream_id
    }

    pub fn remove_stream(&mut self, stream_id: StreamId) {
        self.streams.remove(&stream_id);
        // Update backpressure status after removing stream
        self.update_read_backpressure();
    }

    pub fn get_stream_mut(&mut self, stream_id: StreamId) -> Option<&mut StreamState> {
        self.streams.get_mut(&stream_id)
    }

    pub fn enqueue_frame(&mut self, frame: Frame) -> bool {
        // Check if queue is full - return false to indicate backpressure
        if self.outbound_frames.len() >= MAX_OUTBOUND_FRAME_QUEUE_SIZE {
            tracing::debug!(
                "Outbound frame queue full ({} frames), applying backpressure",
                MAX_OUTBOUND_FRAME_QUEUE_SIZE
            );
            return false;
        }
        
        self.outbound_frames.push_back(frame);
        self.connection_waker.wake();
        true
    }

    pub fn dequeue_frame(&mut self) -> Option<Frame> {
        self.outbound_frames.pop_front()
    }
    
    /// Check if any stream's read buffer is getting full and update backpressure status
    pub fn update_read_backpressure(&mut self) {
        let was_backpressure = self.read_backpressure;
        
        // Calculate total buffer usage across all streams
        let total_buffer_size: usize = self.streams.values()
            .map(|s| s.read_buffer.len())
            .sum();
        
        // TCP Multiplexing backpressure strategy:
        // - Pause when any stream reaches 85% (870KB) to prevent overflow
        // - Resume when all streams drop below 50% (512KB) to ensure stability
        // This provides 35% hysteresis to prevent oscillation

        let pause_threshold = MAX_STREAM_READ_BUFFER_SIZE * 85 / 100;  // 870KB
        let resume_threshold = MAX_STREAM_READ_BUFFER_SIZE * 50 / 100; // 512KB

        let any_stream_near_full = self.streams.values()
            .any(|s| s.read_buffer.len() >= pause_threshold);

        let all_streams_have_space = self.streams.values()
            .all(|s| s.read_buffer.len() <= resume_threshold);

        // Update backpressure state with hysteresis
        if !self.read_backpressure && any_stream_near_full {
            self.read_backpressure = true;
        } else if self.read_backpressure && all_streams_have_space {
            self.read_backpressure = false;
        }
        
        // Add debug logging for persistent backpressure
        if self.read_backpressure && total_buffer_size == 0 {
            tracing::warn!(
                "BACKPRESSURE BUG: Backpressure active but all buffers empty! streams={}, any_stream_full={}, total_threshold={}",
                self.streams.len(),
                any_stream_full,
                total_threshold
            );
            // Force reset backpressure if all buffers are empty
            self.read_backpressure = false;
        }
        
        // Log when backpressure status changes with detailed information
        if self.read_backpressure != was_backpressure {
            if self.read_backpressure {
                tracing::debug!(
                    "Applying read backpressure: total_buffer={} bytes, threshold={} bytes, streams={}, any_stream_full={}",
                    total_buffer_size,
                    total_threshold,
                    self.streams.len(),
                    any_stream_full
                );
                // Log individual stream buffer sizes
                for (stream_id, stream_state) in &self.streams {
                    if stream_state.read_buffer.len() > 0 {
                        tracing::debug!(
                            "Stream {} buffer: {} bytes ({}% of max)",
                            stream_id,
                            stream_state.read_buffer.len(),
                            (stream_state.read_buffer.len() * 100) / MAX_STREAM_READ_BUFFER_SIZE
                        );
                    }
                }
            } else {
                tracing::debug!("Releasing read backpressure: total_buffer={} bytes", total_buffer_size);
                // Wake up the reader task when backpressure is released
                self.backpressure_waker.wake();
            }
        }

    }
    
    /// Check if we should accept new incoming frames
    pub fn should_accept_reads(&self) -> bool {
        !self.read_backpressure
    }

    pub fn handle_incoming_frame(&mut self, frame: Frame) -> bool {
        // First update backpressure status
        self.update_read_backpressure();
        
        let stream_id = frame.header().stream_id();

        if let Some(stream_state) = self.streams.get_mut(&stream_id) {
            match frame.header().command() {
                Command::Psh => {
                    // Check if we should drop this frame due to backpressure
                    if stream_state.read_buffer.len() + frame.data().len() > MAX_STREAM_READ_BUFFER_SIZE {
                        tracing::debug!(
                            "Stream {} read buffer would exceed max size ({} bytes), current: {} bytes, frame: {} bytes - applying backpressure",
                            stream_id,
                            MAX_STREAM_READ_BUFFER_SIZE,
                            stream_state.read_buffer.len(),
                            frame.data().len()
                        );
                        // Return false to indicate backpressure should be applied
                        return false;
                    }
                    
                    stream_state.read_buffer.extend_from_slice(frame.data());
                    stream_state.is_readable = true;
                    stream_state.wake_readers();
                    
                    // Update backpressure after adding data
                    self.update_read_backpressure();
                }
                Command::SynAck => {
                    if stream_state.status == StreamStatus::Opening {
                        stream_state.status = StreamStatus::Open;
                        stream_state.is_writable = true;
                        stream_state.wake_writers();
                    }
                }
                Command::Fin => {
                    // Mark that we received a FIN frame
                    stream_state.fin_received = true;
                    // Close the read end (no more data will be received)
                    stream_state.close_read();
                    tracing::debug!("Stream {} received FIN, read end closed", stream_id);
                }
                Command::Settings => {
                    // 处理设置帧
                }
                Command::Syn => {
                    // 处理同步帧
                }
                Command::Waste => todo!(),
                Command::Alert => {
                    let alert_message = String::from_utf8_lossy(frame.data());
                    tracing::warn!("Server sent Alert: {}", alert_message);
                    tracing::info!("Connection will be closed due to Alert frame");
                    // 关闭连接
                    self.connection_status = ConnectionStatus::Disconnected;
                    self.connection_waker.wake();
                    // 关闭所有流
                    for stream in self.streams.values_mut() {
                        stream.status = StreamStatus::Closed;
                        stream.is_readable = true;
                    }
                },
                Command::UpdatePaddingScheme => todo!(),
                Command::HeartRequest => todo!(),
                Command::HeartResponse => todo!(),
                Command::ServerSettings => todo!(),
            }
        }
        true // Frame was processed successfully
    }
}
