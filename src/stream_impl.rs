use crate::frame::{Frame, StreamId};
use crate::shared_state::{SharedState, StreamStatus};
use bytes::Bytes;
use parking_lot::Mutex;
use std::io;
use std::pin::Pin;
use std::sync::Arc;
use std::task::{Context, Poll};
use tokio::io::{AsyncRead, AsyncWrite, ReadBuf};

#[derive(Debug)]
pub struct Stream {
    stream_id: StreamId,
    shared_state: Arc<Mutex<SharedState>>,
}

impl Stream {
    pub fn new(stream_id: StreamId, shared_state: Arc<Mutex<SharedState>>) -> Self {
        Self {
            stream_id,
            shared_state,
        }
    }

    pub fn stream_id(&self) -> StreamId {
        self.stream_id
    }
}

impl AsyncRead for Stream {
    fn poll_read(
        self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &mut ReadBuf<'_>,
    ) -> Poll<io::Result<()>> {
        let mut shared_state = self.shared_state.lock();

        // Check connection status first
        let connection_disconnected =
            shared_state.connection_status == crate::shared_state::ConnectionStatus::Disconnected;

        let stream_state = match shared_state.get_stream_mut(self.stream_id) {
            Some(state) => state,
            None => {
                return Poll::Ready(Err(io::Error::new(
                    io::ErrorKind::NotConnected,
                    "Stream not found",
                )));
            }
        };

        match stream_state.status {
            StreamStatus::Closed | StreamStatus::ReadClosed => {
                if stream_state.read_buffer.is_empty() {
                    return Poll::Ready(Ok(())); // Return EOF
                }
            }
            StreamStatus::Open | StreamStatus::WriteClosed | StreamStatus::Closing => {}
            _ => {
                stream_state.read_waker.register(cx.waker());
                return Poll::Pending;
            }
        }

        if stream_state.read_buffer.is_empty() {
            if stream_state.should_return_eof() || connection_disconnected {
                return Poll::Ready(Ok(())); // Return EOF
            }

            stream_state.read_waker.register(cx.waker());
            return Poll::Pending;
        }

        let available = std::cmp::min(buf.remaining(), stream_state.read_buffer.len());
        let data = stream_state.read_buffer.split_to(available);
        buf.put_slice(&data);
        
        // Update flow control state after consuming data
        stream_state.update_flow_control();

        // No connection-level backpressure in TCP multiplexing - each stream manages itself

        Poll::Ready(Ok(()))
    }
}

impl AsyncWrite for Stream {
    fn poll_write(
        self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &[u8],
    ) -> Poll<Result<usize, io::Error>> {
        let mut shared_state = self.shared_state.lock();

        let stream_state = match shared_state.get_stream_mut(self.stream_id) {
            Some(state) => state,
            None => {
                return Poll::Ready(Err(io::Error::new(
                    io::ErrorKind::NotConnected,
                    "Stream not found",
                )));
            }
        };

        match stream_state.status {
            StreamStatus::Open | StreamStatus::ReadClosed => {
                if !stream_state.can_write() {
                    return Poll::Ready(Err(io::Error::new(
                        io::ErrorKind::BrokenPipe,
                        "Write end is closed",
                    )));
                }
            }
            StreamStatus::Opening => {
                stream_state.write_waker.register(cx.waker());
                return Poll::Pending;
            }
            StreamStatus::WriteClosed | StreamStatus::Closing | StreamStatus::Closed => {
                return Poll::Ready(Err(io::Error::new(
                    io::ErrorKind::BrokenPipe,
                    "Write end is closed",
                )));
            }
        }

        // Use smaller chunks for better memory management in high-throughput scenarios
        const CHUNK_SIZE: usize = 16 * 1024; // 16KB chunks
        let max_size = std::cmp::min(buf.len(), CHUNK_SIZE);
        let data_to_write = &buf[..max_size];
        
        let data = Bytes::copy_from_slice(data_to_write);
        let frame = Frame::new_psh(self.stream_id, data);
        
        // Release the lock before enqueue_frame
        drop(shared_state);
        
        // Apply backpressure if queue is full
        if !self.shared_state.lock().enqueue_frame(frame) {
            // Register waker and return Pending to apply backpressure
            let mut shared_state = self.shared_state.lock();
            if let Some(stream_state) = shared_state.get_stream_mut(self.stream_id) {
                stream_state.write_waker.register(cx.waker());
            }
            return Poll::Pending;
        }

        Poll::Ready(Ok(max_size))
    }

    fn poll_flush(self: Pin<&mut Self>, _cx: &mut Context<'_>) -> Poll<Result<(), io::Error>> {
        Poll::Ready(Ok(()))
    }

    fn poll_shutdown(self: Pin<&mut Self>, _cx: &mut Context<'_>) -> Poll<Result<(), io::Error>> {
        let mut shared_state = self.shared_state.lock();

        if let Some(stream_state) = shared_state.get_stream_mut(self.stream_id) {
            // Only send FIN if we haven't already sent one
            if !stream_state.fin_sent {
                stream_state.fin_sent = true;
                stream_state.close_write();

                let fin_frame = Frame::new_fin(self.stream_id);
                let _ = shared_state.enqueue_frame(fin_frame);

                tracing::debug!("Stream {} sent FIN, write end closed", self.stream_id);
            }
        }

        Poll::Ready(Ok(()))
    }
}

impl Drop for Stream {
    fn drop(&mut self) {
        let mut shared_state = self.shared_state.lock();
        // Send FIN frame if write end is still open and we haven't sent FIN yet
        if let Some(stream_state) = shared_state.get_stream_mut(self.stream_id) {
            if !stream_state.fin_sent && stream_state.can_write() {
                stream_state.fin_sent = true;
                stream_state.close_write();
                let fin_frame = Frame::new_fin(self.stream_id);
                let _ = shared_state.enqueue_frame(fin_frame);
                tracing::debug!("Stream {} dropped, sent FIN", self.stream_id);
            }
        }
        shared_state.remove_stream(self.stream_id);
    }
}
