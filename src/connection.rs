use std::sync::atomic::AtomicBool;
use std::sync::{Arc};
use std::task::Poll;
use parking_lot::Mutex;
use tokio::net::TcpStream;
use tokio_native_tls::TlsStream;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tokio_util::codec::{FramedRead, FramedWrite};
use futures::stream::StreamExt;
use futures::sink::SinkExt;
use tokio::io::AsyncWriteExt;

use crate::frame::Codec;
use crate::shared_state::{SharedState, ConnectionStatus};
use crate::stream_impl::Stream;
use crate::sock_addr::SockAddr;

#[derive(Debug, thiserror::Error)]
pub enum ConnectionError {
    #[error("Connection timeout")]
    Timeout,
    #[error("Connection failed: {0}")]
    Failed(String),
    #[error("Stream creation failed")]
    StreamCreationFailed,
    #[error("Lock error")]
    LockError,
}

pub struct Connection {
    shared_state: Arc<Mutex<SharedState>>,
    // 是否发送过setting
    sent_settings: AtomicBool,
    _reader_handle: Join<PERSON>and<PERSON><()>,
    _writer_handle: JoinHandle<()>,
}

impl Connection {
    pub fn new(tls_stream: TlsStream<TcpStream>) -> Self {
        let shared_state = Arc::new(Mutex::new(SharedState::new()));
        
        let (read_half, write_half) = tokio::io::split(tls_stream);
        
        // Start reader task
        let reader_state = Arc::clone(&shared_state);
        let reader_handle = tokio::spawn(async move {
            Self::reader_task(read_half, reader_state).await;
        });

        // Start writer task
        let writer_state = Arc::clone(&shared_state);
        let writer_handle = tokio::spawn(async move {
            Self::writer_task(write_half, writer_state).await;
        });

        // Set connection as connected
        {
            let mut state = shared_state.lock();
            state.connection_status = ConnectionStatus::Connected;
        }

        Self {
            shared_state,
            _reader_handle: reader_handle,
            _writer_handle: writer_handle,
            sent_settings: AtomicBool::new(false),
        }
    }

    pub async fn open_stream(&self, target_address: SockAddr) -> Result<Stream, ConnectionError> {
        let stream_id = {
            let mut state = self.shared_state.lock();
            
            // Check if connection is still alive
            if state.connection_status == ConnectionStatus::Disconnected {
                return Err(ConnectionError::Failed("Connection closed by server".to_string()));
            }
            
            let stream_id = state.create_stream();
            
            // Set stream as immediately open (no handshake needed)
            if let Some(stream_state) = state.get_stream_mut(stream_id) {
                stream_state.status = crate::shared_state::StreamStatus::Open;
            }

            if !self.sent_settings.load(std::sync::atomic::Ordering::Relaxed) {
                let settings_frame = crate::frame::Frame::new_settings("settings");
                let _ = state.enqueue_frame(settings_frame);
                self.sent_settings.store(true, std::sync::atomic::Ordering::Relaxed);
            }
            
            // Send SYN frame
            let syn_frame = crate::frame::Frame::new_syn(stream_id);
            tracing::debug!("Sending SYN frame for stream {}", stream_id);
            let _ = state.enqueue_frame(syn_frame);

            // Send target address
            let target_bytes = target_address.to_bytes();
            tracing::debug!("Sending target address: {} ({} bytes)", target_address, target_bytes.len());
            let psh_frame = crate::frame::Frame::new_psh(stream_id, target_bytes);
            let _ = state.enqueue_frame(psh_frame);
            
            stream_id
        };

        let stream = Stream::new(stream_id, Arc::clone(&self.shared_state));
        Ok(stream)
    }


    pub async fn close(&self) -> Result<(), ConnectionError> {
        {
            let mut state = self.shared_state.lock();
            
            state.connection_status = ConnectionStatus::Disconnecting;
            
            // Send FIN frames for all streams that haven't sent FIN yet
            let mut fin_frames = Vec::new();
            for (stream_id, stream_state) in state.streams.iter_mut() {
                if !stream_state.fin_sent && stream_state.can_write() {
                    stream_state.fin_sent = true;
                    stream_state.close_write();
                    fin_frames.push(crate::frame::Frame::new_fin(*stream_id));
                }
            }
            
            for frame in fin_frames {
                let _ = state.enqueue_frame(frame);
            }
        }
        
        // Wait for frames to be sent
        tokio::time::sleep(std::time::Duration::from_millis(200)).await;
        
        Ok(())
    }

    pub fn is_connected(&self) -> bool {
        let state = self.shared_state.lock();
        matches!(state.connection_status, ConnectionStatus::Connected)
    }

    async fn reader_task(
        read_half: tokio::io::ReadHalf<TlsStream<TcpStream>>,
        shared_state: Arc<Mutex<SharedState>>,
    ) {
        let mut framed_read = FramedRead::new(read_half, Codec {});
        
        loop {
            // Check if we should pause reading due to backpressure
            let should_read = {
                let state = shared_state.lock();
                state.should_accept_reads()
            };
            
            if !should_read {
                // Instead of polling, wait for backpressure to be released with periodic checks
                tracing::debug!("Reader task pausing due to backpressure - waiting for wakeup");

                let backpressure_released = std::future::poll_fn(|cx| {
                    let mut state = shared_state.lock();

                    // Periodically re-check backpressure status in case it wasn't updated
                    state.update_read_backpressure();

                    if state.should_accept_reads() {
                        Poll::Ready(())
                    } else {
                        state.backpressure_waker.register(cx.waker());
                        Poll::Pending
                    }
                });

                // Add a timeout to force periodic re-checks
                let timeout = tokio::time::sleep(std::time::Duration::from_millis(100));

                tokio::select! {
                    _ = backpressure_released => {
                        tracing::debug!("Reader task resumed - backpressure released");
                    }
                    _ = timeout => {
                        tracing::debug!("Reader task timeout - rechecking backpressure");
                    }
                }
                continue;
            }
            
            match framed_read.next().await {
                Some(Ok(frame)) => {
                    tracing::trace!("Received frame: {:?}", frame.header());
                    if frame.header().command() == crate::frame::Command::Alert {
                        tracing::warn!("Alert frame received, data: {:?}", frame.data());
                        tracing::warn!("Alert frame text: {}", String::from_utf8_lossy(frame.data()));
                    }
                    
                    // Handle the frame
                    let frame_processed = {
                        let mut state = shared_state.lock();
                        state.handle_incoming_frame(frame)
                    };
                    
                    // If frame couldn't be processed due to buffer full, we'll catch it in the next loop iteration
                    if !frame_processed {
                        tracing::debug!("Frame processing failed due to buffer constraints");
                        // The backpressure check at the start of the loop will handle the pause
                    }
                }
                Some(Err(e)) => {
                    tracing::error!("Frame decode error: {}", e);
                    break;
                }
                None => {
                    tracing::info!("Connection closed by server");
                    break;
                }
            }
        }

        // Only mark as disconnected when we actually get EOF
        tracing::info!("Connection closed by server");
        {
            let mut state = shared_state.lock();
            state.connection_status = ConnectionStatus::Disconnected;
            
            // Wake up all streams waiting for data
            for stream_state in state.streams.values_mut() {
                stream_state.wake_readers();
                stream_state.wake_writers();
            }
            
            // Wake up any task waiting for backpressure release
            state.backpressure_waker.wake();
        }
    }

    async fn writer_task(
        write_half: tokio::io::WriteHalf<TlsStream<TcpStream>>,
        shared_state: Arc<Mutex<SharedState>>,
    ) {
        let mut framed_write = FramedWrite::new(write_half, Codec {});
        
        loop {
            let frame_opt = {
                let mut state = shared_state.lock();

                if state.connection_status == ConnectionStatus::Disconnected 
                    || state.connection_status == ConnectionStatus::Disconnecting {
                    // Check if there are any remaining frames to send
                    if state.outbound_frames.is_empty() {
                        break;
                    }
                }

                state.dequeue_frame()
            };

            let frame = match frame_opt {
                Some(frame) => frame,
                None => {
                    // Use futures::future::poll_fn to wait for frames
                    let frame_available = std::future::poll_fn(|cx| {
                        let state = shared_state.lock();
                        if !state.outbound_frames.is_empty() {
                            Poll::Ready(Some(()))
                        } else if state.connection_status == ConnectionStatus::Disconnected {
                            // Connection is disconnected, exit
                            Poll::Ready(None)
                        } else {
                            state.connection_waker.register(cx.waker());
                            Poll::Pending
                        }
                    });
                    
                    match frame_available.await {
                        Some(()) => continue,
                        None => break, // Connection disconnected
                    }
                }
            };

            if let Err(e) = framed_write.send(frame).await {
                tracing::error!("Writer error: {}", e);
                break;
            }
            
            // Wake up any writers waiting due to backpressure
            {
                let mut state = shared_state.lock();
                for stream_state in state.streams.values_mut() {
                    stream_state.wake_writers();
                }
            }
        }

        // Flush and close properly
        if let Err(e) = framed_write.flush().await {
            tracing::error!("Failed to flush writer: {}", e);
        }

        if let Err(e) = framed_write.get_mut().shutdown().await {
            tracing::error!("Failed to shutdown write half: {}", e);
        }

        {
            let mut state = shared_state.lock();
            state.connection_status = ConnectionStatus::Disconnected;
            
            // Wake up all streams waiting for data
            for stream_state in state.streams.values_mut() {
                stream_state.wake_readers();
                stream_state.wake_writers();
            }
            
            // Wake up any task waiting for backpressure release
            state.backpressure_waker.wake();
        }
    }
}

impl Drop for Connection {
    fn drop(&mut self) {
        {
            let mut state = self.shared_state.lock();
            state.connection_status = ConnectionStatus::Disconnecting;
            
            // Wake up any waiting tasks
            state.connection_waker.wake();
            state.backpressure_waker.wake();
            
            // Wake up all streams
            for stream_state in state.streams.values_mut() {
                stream_state.wake_readers();
                stream_state.wake_writers();
            }
        }
    }
}